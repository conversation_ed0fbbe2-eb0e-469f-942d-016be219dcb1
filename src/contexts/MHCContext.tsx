/**
 * ========================================================================
 * MHCContext - Contexto de Hardware Mobile Communications
 * ========================================================================
 *
 * PROPÓSITO:
 * - Gestiona la comunicación con el hardware del dispositivo móvil
 * - Proporciona funcionalidades nativas como speech, timeout, WebView control
 * - Maneja el estado de conexión y capacidades del dispositivo
 * - Ofrece operaciones específicas para el juego (anuncios, progreso)
 *
 * RESPONSABILIDADES:
 * ✅ Conexión MHC: Estado, inicialización y reconexión
 * ✅ Funciones Core: Speech, timeout, WebView, mensajería
 * ✅ Device Management: Capacidades, información del dispositivo
 *
 * INTEGRACIÓN:
 * - Usado por SpeechInputContext para transcripción
 * - Usado por GameOrchestratorContext para coordinación
 * - Proporciona fallbacks cuando hardware no está disponible
 * ========================================================================
 */

import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  useMemo,
  type ReactNode,
} from "react";
import { transcriptionService, type IMHC } from "../services/TranscriptionService";


import type { MHCState, MHCCapabilities, MHCContextProps } from "../models";

// ========== CONTEXT ==========
const MHCContext = createContext<MHCContextProps | undefined>(undefined);

/**
 * Hook personalizado para acceder al contexto MHC
 * Incluye validación de uso dentro del Provider
 */
export const useMHC = () => {
  const context = useContext(MHCContext);
  if (!context) {
    throw new Error("useMHC must be used within MHCProvider");
  }
  return context;
};

// ========== PROVIDER OPTIMIZADO ==========
export const MHCProvider = ({ children }: { children: ReactNode }) => {
  // ========== ESTADOS LOCALES ==========
  const [state, setState] = useState<MHCState>({
    isAvailable: false,
    deviceId: "",
    connectionStatus: "disconnected",
    capabilities: {
      canSpeak: false,
      canCloseWebView: false,
      canSetTimeout: false,
      canHideAura: false,
      canSendMessages: false,
    },
    errorMessage: null,
  });

  const [mhcInstance, setMhcInstance] = useState<IMHC | null>(null);

  // ========== FUNCIONES AUXILIARES MEMOIZADAS ==========
  /**
   * Probar todas las capacidades MHC
   * Memoizada para evitar recálculos innecesarios
   */
  const testAllFeatures = useCallback(async (): Promise<MHCCapabilities> => {
    if (!mhcInstance) {
      return {
        canSpeak: false,
        canCloseWebView: false,
        canSetTimeout: false,
        canHideAura: false,
        canSendMessages: false,
      };
    }

    const capabilities: MHCCapabilities = {
      canSpeak: false,
      canCloseWebView: false,
      canSetTimeout: false,
      canHideAura: false,
      canSendMessages: false,
    };

    try {
      mhcInstance.getId();
      capabilities.canSpeak = true;
      capabilities.canCloseWebView = true;
      capabilities.canSetTimeout = true;
      capabilities.canHideAura = true;
      capabilities.canSendMessages = true;
    } catch (error) {
      console.warn("⚠️ Algunas funcionalidades MHC no están disponibles:", error);
    }

    return capabilities;
  }, [mhcInstance]);

  /**
   * Inicializar MHC con manejo completo de errores
   */
  const initializeMHC = useCallback(async () => {
    setState(prev => ({ ...prev, connectionStatus: "connecting" }));

    try {
      // Obtener instancia MHC del servicio de transcripción
      const mhc = transcriptionService.getMHC();
      setMhcInstance(mhc);

      // Verificar disponibilidad
      const isAvailable = transcriptionService.isMHCAvailable();

      if (isAvailable) {
        const deviceId = mhc.getId();

        let capabilities: MHCCapabilities = {
          canSpeak: false,
          canCloseWebView: false,
          canSetTimeout: false,
          canHideAura: false,
          canSendMessages: false,
        };

        try {
          mhc.getId();
          capabilities = {
            canSpeak: true,
            canCloseWebView: true,
            canSetTimeout: true,
            canHideAura: true,
            canSendMessages: true,
          };
        } catch (error) {
          console.warn("⚠️ Algunas funcionalidades MHC no están disponibles:", error);
        }

        setState(prev => ({
          ...prev,
          isAvailable: true,
          deviceId,
          connectionStatus: "connected",
          capabilities,
          errorMessage: null,
        }));

        console.log("✅ MHC inicializado correctamente", { deviceId, capabilities });
      } else {
        setState(prev => ({
          ...prev,
          isAvailable: false,
          connectionStatus: "disconnected",
          errorMessage: "MHC no disponible en este dispositivo",
        }));

        console.warn("⚠️ MHC no disponible");
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Error inicializando MHC";

      setState(prev => ({
        ...prev,
        connectionStatus: "error",
        errorMessage,
      }));

      console.error("❌ Error inicializando MHC:", error);
    }
  }, []);

  // ========== EFECTOS ==========
  /**
   * Inicialización automática al montar el componente
   */
  useEffect(() => {
    console.log("✅ [MHCProvider] Inicializando MHC");
    initializeMHC();

    return () => {
      console.log("ℹ️ [MHCProvider] Limpiando MHCProvider");
    };
  }, [initializeMHC]);

  // ========== FUNCIONES CORE MEMOIZADAS ==========
  /**
   * Configurar timeout del sistema
   */
  const setSucwTimeout = useCallback((timeMS: number) => {
    if (!mhcInstance || !state.isAvailable) {
      console.warn("⚠️ [MHCProvider] MHC no disponible para setSucwTimeout");
      return;
    }

    try {
      mhcInstance.setSucwTimeout(timeMS);
      console.log(`✅ [MHCProvider] Timeout establecido: ${timeMS}ms`);
    } catch (error) {
      console.error("❌ [MHCProvider] Error estableciendo timeout:", error);
    }
  }, [mhcInstance, state.isAvailable]);

  /**
   * Cerrar WebView/aplicación
   */
  const closeWebView = useCallback(() => {
    if (!mhcInstance || !state.isAvailable) {
      console.warn("⚠️ [MHCProvider] MHC no disponible para closeWebView");
      return;
    }

    try {
      mhcInstance.closeWebView();
      console.log("✅ [MHCProvider] WebView cerrado");
    } catch (error) {
      console.error("❌ [MHCProvider] Error cerrando WebView:", error);
    }
  }, [mhcInstance, state.isAvailable]);

  /**
   * Ocultar asistente virtual
   */
  const hideAura = useCallback(() => {
    if (!mhcInstance || !state.isAvailable) {
      console.warn("⚠️ [MHCProvider] MHC no disponible para hideAura");
      return;
    }

    try {
      mhcInstance.hideAura();
      console.log("✅ [MHCProvider] Aura ocultado");
    } catch (error) {
      console.error("❌ [MHCProvider] Error ocultando Aura:", error);
    }
  }, [mhcInstance, state.isAvailable]);

  /**
   * Hacer hablar al asistente virtual
   */
  const speakAura = useCallback((text: string) => {
    if (!mhcInstance || !state.isAvailable) {
      console.warn("⚠️ [MHCProvider] MHC no disponible para speakAura");
      return;
    }

    try {
      mhcInstance.speakAura(text);
      console.log(`✅ [MHCProvider] Aura hablando: "${text.substring(0, 50)}..."`);
    } catch (error) {
      console.error("❌ [MHCProvider] Error haciendo hablar a Aura:", error);
    }
  }, [mhcInstance, state.isAvailable]);

  /**
   * Enviar mensaje al asistente virtual
   */
  const sendAura = useCallback((text: string) => {
    if (!mhcInstance || !state.isAvailable) {
      console.warn("⚠️ [MHCProvider] MHC no disponible para sendAura");
      return;
    }

    try {
      mhcInstance.sendAura(text);
      console.log(`✅ [MHCProvider] Mensaje enviado a Aura: "${text.substring(0, 50)}..."`);
    } catch (error) {
      console.error("❌ [MHCProvider] Error enviando mensaje a Aura:", error);
    }
  }, [mhcInstance, state.isAvailable]);

  /**
   * Obtener ID del dispositivo con fallback
   */
  const getId = useCallback((): string => {
    if (!mhcInstance) {
      console.warn("⚠️ [MHCProvider] MHC no disponible para getId, usando fallback");
      return `fallback-${Date.now()}`;
    }

    try {
      const id = mhcInstance.getId();
      console.log(`🔍 [MHCProvider] 🆔 Device ID: ${id}`);
      return id;
    } catch (error) {
      console.error("❌ [MHCProvider] ❌ Error obteniendo ID:", error);
      return `error-${Date.now()}`;
    }
  }, [mhcInstance]);

  // ========== OPERACIONES MEJORADAS ==========
  /**
   * Hablar y ocultar con delay personalizable
   */
  const speakAndHide = useCallback(async (text: string, hideDelay: number = 2000): Promise<void> => {
    if (!state.capabilities.canSpeak || !state.capabilities.canHideAura) {
      console.warn("⚠️ [MHCProvider] Funcionalidades requeridas no disponibles para speakAndHide");
      return;
    }

    try {
      console.log(`ℹ️ [MHCProvider] Hablar y ocultar: "${text.substring(0, 50)}..."`);

      // Usar funciones del contexto directamente
      if (mhcInstance) {
        mhcInstance.speakAura(text);

        // Esperar y luego ocultar
        setTimeout(() => {
          if (mhcInstance) {
            mhcInstance.hideAura();
          }
        }, hideDelay);
      }

    } catch (error) {
      console.error("❌ [MHCProvider] Error en speakAndHide:", error);
    }
  }, [state.capabilities.canSpeak, state.capabilities.canHideAura, mhcInstance]);

  /**
   * Configurar timeout de aplicación en segundos
   */
  const setAppTimeout = useCallback((seconds: number) => {
    const timeMS = seconds * 1000;

    if (mhcInstance && state.isAvailable) {
      try {
        mhcInstance.setSucwTimeout(timeMS);
        console.log(`✅ [MHCProvider] Timeout de aplicación establecido: ${seconds}s`);
      } catch (error) {
        console.error("❌ [MHCProvider] Error estableciendo timeout de app:", error);
      }
    }
  }, [mhcInstance, state.isAvailable]);

  // ========== GESTIÓN DE CONEXIÓN ==========
  /**
   * Verificar estado de conexión actual
   */
  const checkConnection = useCallback(async (): Promise<boolean> => {
    try {
      if (!mhcInstance) return false;

      // Test simple: intentar obtener ID
      const id = mhcInstance.getId();
      const isConnected = Boolean(id);

      setState(prev => ({
        ...prev,
        connectionStatus: isConnected ? "connected" : "disconnected"
      }));

      return isConnected;
    } catch (error) {
      setState(prev => ({
        ...prev,
        connectionStatus: "error",
        errorMessage: "Error verificando conexión MHC"
      }));
      return false;
    }
  }, [mhcInstance]);

  /**
   * Reconectar MHC
   */
  const reconnect = useCallback(async (): Promise<boolean> => {
    console.log("ℹ️ [MHCProvider] 🔄 Intentando reconexión MHC");

    setState(prev => ({ ...prev, connectionStatus: "connecting" }));

    try {
      await initializeMHC();
      return transcriptionService.isMHCAvailable();
    } catch (error) {
      console.error("❌ [MHCProvider] ❌ Error en reconexión:", error);
      return false;
    }
  }, [initializeMHC]);

  /**
   * Obtener información completa del dispositivo
   */
  const getDeviceInfo = useCallback((): object => {
    return {
      isAvailable: state.isAvailable,
      deviceId: state.deviceId,
      connectionStatus: state.connectionStatus,
      capabilities: state.capabilities,
      mhcVersion: "unknown",
      timestamp: new Date().toISOString(),
    };
  }, [state]);

  // ========== UTILIDADES ==========
  /**
   * Verificar si una capacidad específica está disponible
   */
  const isFeatureAvailable = useCallback((feature: keyof MHCCapabilities): boolean => {
    return state.capabilities[feature];
  }, [state.capabilities]);

  /**
   * Reset completo del estado MHC
   */
  const reset = useCallback(() => {
    console.log("ℹ️ [MHCProvider] 🔄 Reseteando MHC");

    setState({
      isAvailable: false,
      deviceId: "",
      connectionStatus: "disconnected",
      capabilities: {
        canSpeak: false,
        canCloseWebView: false,
        canSetTimeout: false,
        canHideAura: false,
        canSendMessages: false,
      },
      errorMessage: null,
    });

    setMhcInstance(null);
  }, []);

  // ========== VALOR DEL CONTEXTO MEMOIZADO ==========
  const contextValue = useMemo<MHCContextProps>(() => ({
    // Estado
    state,

    // Funcionalidad Core MHC
    setSucwTimeout,
    closeWebView,
    hideAura,
    speakAura,
    sendAura,
    getId,

    // Operaciones mejoradas
    speakAndHide,
    setAppTimeout,

    // Gestión de conexión
    checkConnection,
    reconnect,
    getDeviceInfo,

    // Utilidades
    isFeatureAvailable,
    testAllFeatures,
    reset,
  }), [
    // ✅ DEPENDENCIAS EXPLÍCITAS: Solo las que realmente cambian
    state,
    setSucwTimeout,
    closeWebView,
    hideAura,
    speakAura,
    sendAura,
    getId,
    speakAndHide,
    setAppTimeout,
    checkConnection,
    reconnect,
    getDeviceInfo,
    isFeatureAvailable,
    testAllFeatures,
    reset,
  ]);

  return (
    <MHCContext.Provider value={contextValue}>
      {children}
    </MHCContext.Provider>
  );
};
